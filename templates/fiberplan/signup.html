<!-- templates/fiberPlan/signup.html -->
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>ESVC Fiber Sign-Up</title>
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <link rel="stylesheet" href="{% static 'css/main.css' %}">
  <style>
    body { font-family: Arial, sans-serif; background: #f8fafc; margin: 0; }
    .signup-container { max-width: 740px; margin: 2rem auto; background: #fff; padding: 2rem 2.5rem; border-radius: 12px; box-shadow: 0 2px 16px #0002;}
    .section-title { font-size: 1.2rem; margin-bottom: 0.5rem; font-weight: bold;}
    .form-field { margin-bottom: 1rem;}
    label { display: block; margin-bottom: 0.35rem; font-weight: 500;}
    input, select, textarea { width: 100%; padding: 0.6em; border: 1px solid #bbb; border-radius: 6px; }
    .row { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; }
    .legal { font-size: 0.95em; background: #f1f5f9; padding: 1em; border-radius: 8px; margin-bottom: 1em;}
    .signature-block { margin-top: 1.5em;}
    .submit-btn { width: 100%; background: #134e4a; color: #fff; padding: 0.85em; font-size: 1.15em; border-radius: 7px; border: none; font-weight: bold; cursor: pointer;}
    .submit-btn:disabled { background: #ccc;}
    .logo { width: 140px; margin-bottom: 1rem;}
    .bg-light { background: #f1f5f9; }
    .inline { display: inline-flex; align-items: center; gap: .5rem; }
  </style>
</head>
<body>
  <div class="signup-container">
    <h2>Fiber Internet Sign-Up Form</h2>
    <p>Thank you for choosing Eastern Shore Communications. Please complete your information below to finalize your service agreement.</p>

    <form method="post" autocomplete="off">
      {% csrf_token %}
      <input type="hidden" name="token" value="{{ token }}">

      <div class="section-title">Contact Information</div>
      <div class="form-field">
        <label>Name</label>
        <input type="text" value="{{ customer.firstName }} {{ customer.lastName }}" disabled>
      </div>
      <div class="form-field">
        <label>Email</label>
        <input type="email" value="{{ customer.email }}" disabled>
      </div>

      <!-- Service Address -->
      <div class="section-title" style="margin-top:1rem;">Service Address</div>
      <div class="form-field">
        <label for="service_street">Address</label>
        <input id="service_street" name="service_street" type="text" required
               value="{{ prefill_service_street }}">
      </div>
      <div class="row">
        <div class="form-field">
          <label for="service_city">City</label>
          <input id="service_city" name="service_city" type="text" required
                 value="{{ prefill_service_city }}">
        </div>
        <div class="form-field">
          <label for="service_state">State</label>
          <input id="service_state" name="service_state" type="text" maxlength="2" required
                 value="{{ prefill_service_state }}">
        </div>
      </div>

      <!-- Billing Address -->
      <div class="section-title" style="margin-top:1rem;">
        Billing Address
        <label style="float:right; font-weight:normal;">
          <input id="same_as_service" type="checkbox" checked> Same As Service Address
        </label>
      </div>
      <div class="form-field">
        <label for="billing_street">Address</label>
        <input id="billing_street" name="billing_street" type="text" required
               value="{{ prefill_billing_street }}">
      </div>
      <div class="row">
        <div class="form-field">
          <label for="billing_city">City</label>
          <input id="billing_city" name="billing_city" type="text" required
                 value="{{ prefill_billing_city }}">
        </div>
        <div class="form-field">
          <label for="billing_state">State</label>
          <input id="billing_state" name="billing_state" type="text" maxlength="2" required
                 value="{{ prefill_billing_state }}">
        </div>
      </div>

      <div class="section-title">Select Your Plan</div>
      <div class="form-field">
        <select name="plan_selected" required>
          <option value="">-- Choose Plan --</option>
          <option value="Bronze">Bronze: $25/mo (25 Mbps/10 Mbps)</option>
          <option value="Silver">Silver: $60/mo (250 Mbps/10 Mbps) - Most Popular</option>
          <option value="Gold">Gold: $90/mo (500 Mbps/20 Mbps)</option>
        </select>
      </div>

      <!-- Fees -->
      <div class="row">
        <div class="form-field">
          <label for="install_fee">Internet Installation and Setup Fee</label>
          <input id="install_fee" name="install_fee" type="number" step="0.01" value="">
        </div>
        <div class="form-field">
          <label for="network_maintenance_fee">Network Maintenance Fee</label>
          <input id="network_maintenance_fee" name="network_maintenance_fee" type="number" step="0.01" value="7.95">
        </div>
      </div>

      <!-- Equipment choices + driven fees -->
      <div class="section-title">Equipment</div>
      <div class="form-field">
        <label class="inline">
          <input type="checkbox" id="cb_modem"> Fiber Modem (Lease $4.95/mo)
        </label>
      </div>
      <div class="form-field">
        <label class="inline">
          <input type="checkbox" id="cb_combo"> Fiber Modem and Router Combo (Lease $15.00/mo)
        </label>
      </div>

      <div class="row">
        <div class="form-field">
          <label for="modem_lease_fee">Modem Lease Fee</label>
          <input id="modem_lease_fee" name="modem_lease_fee" type="number" step="0.01" value="">
        </div>
        <div class="form-field">
          <label for="router_lease_fee">Router Lease Fee (if applicable)</label>
          <input id="router_lease_fee" name="router_lease_fee" type="number" step="0.01" value="">
        </div>
      </div>

      <div class="form-field">
        <label>Additional Construction Costs (if any)</label>
        <input name="additional_construction_costs" type="number" step="0.01">
      </div>

      <div class="form-field">
        <label>Preferred Installation Date</label>
        <input name="preferred_installation_date" type="date">
      </div>

      <div class="legal">
        <strong>Terms & Conditions:</strong>
        <ul>
          <li>By signing, you agree to the <a href="https://www.esvc.us/terms" target="_blank">Terms and Conditions</a> and Acceptable Internet Use Policy of Eastern Shore Communications, LLC.</li>
          <li>One-year service agreement. Early termination fees may apply.</li>
          <li>Customer Premise Equipment must be returned upon cancellation.</li>
          <li>Service subject to availability and pre-installation engineering.</li>
          <li>See <a href="https://www.esvc.us" target="_blank">www.esvc.us</a> for further details.</li>
        </ul>
      </div>

      <div class="form-field">
        <input type="checkbox" name="agrees_to_terms" required id="agrees_to_terms">
        <label for="agrees_to_terms">I agree to the Terms & Conditions and Acceptable Use Policy.</label>
      </div>

      <div class="form-field signature-block">
        <label for="signature">E-signature (type your full name)</label>
        <input type="text" name="signature" id="signature" required>
      </div>

      <button type="submit" class="submit-btn">Submit & Sign</button>
    </form>
  </div>

  <script>
    (function () {
      // Same-as-service toggle logic (unchanged)
      const same = document.getElementById('same_as_service');

      const sStreet = document.getElementById('service_street');
      const sCity   = document.getElementById('service_city');
      const sState  = document.getElementById('service_state');

      const bStreet = document.getElementById('billing_street');
      const bCity   = document.getElementById('billing_city');
      const bState  = document.getElementById('billing_state');

      function upper2(el){ el.value = (el.value || '').toUpperCase().slice(0,2); }
      ['input','change','blur'].forEach(evt => {
        sState && sState.addEventListener(evt, () => upper2(sState));
        bState && bState.addEventListener(evt, () => upper2(bState));
      });

      function copyFromService() {
        bStreet.value = sStreet.value;
        bCity.value   = sCity.value;
        bState.value  = sState.value.toUpperCase();
      }
      function setBillingDisabled(disabled) {
        [bStreet, bCity, bState].forEach(el => {
          el.readOnly = disabled;
          el.classList.toggle('bg-light', disabled);
        });
      }
      if (same && same.checked) { copyFromService(); setBillingDisabled(true); }
      if (same) {
        same.addEventListener('change', function () {
          if (same.checked) { copyFromService(); setBillingDisabled(true); }
          else { bStreet.value=''; bCity.value=''; bState.value=''; setBillingDisabled(false); bStreet.focus(); }
        });
      }
      [sStreet, sCity, sState].forEach(el => {
        el && el.addEventListener('input', () => { if (same && same.checked) copyFromService(); });
      });

      // Equipment fee toggle logic (NEW)
      const cbModem = document.getElementById('cb_modem');
      const cbCombo = document.getElementById('cb_combo');

      const modemFee  = document.getElementById('modem_lease_fee');
      const routerFee = document.getElementById('router_lease_fee');

      function setModemChecked(on) {
        cbModem.checked = on;
        if (on) {
          modemFee.value = '4.95';
          // mutually exclusive: turn combo off & clear router fee
          cbCombo.checked = false;
          routerFee.value = '';
        } else {
          modemFee.value = '';
        }
      }
      function setComboChecked(on) {
        cbCombo.checked = on;
        if (on) {
          routerFee.value = '15.00';
          // mutually exclusive: turn modem off & clear modem fee
          cbModem.checked = false;
          modemFee.value = '';
        } else {
          routerFee.value = '';
        }
      }

      if (cbModem) {
        cbModem.addEventListener('change', () => setModemChecked(cbModem.checked));
      }
      if (cbCombo) {
        cbCombo.addEventListener('change', () => setComboChecked(cbCombo.checked));
      }
      // Start with neither selected unless you want a default.
      setModemChecked(false);
      setComboChecked(false);

      // Network Maintenance Fee default value already set to 7.95 in markup.
      // Internet Installation and Setup Fee has no placeholder or default value, per requirements.
    })();
  </script>
</body>
</html>
